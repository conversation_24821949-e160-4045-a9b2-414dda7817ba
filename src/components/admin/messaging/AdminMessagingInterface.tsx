import { useState, useEffect, useMemo, useCallback } from 'react';
import { UniversalChat } from '@/components/chat/UniversalChat';
import { useUIHelpers } from '@/hooks/use-ui-helpers';
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MessageSquare, Users, UserCheck, Mail, Search, Wifi, WifiOff, ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useChat } from '@/hooks/useChat';
import { useAutoSelectChat } from '@/hooks/useAutoSelectChat';
import { Chat } from '@/types/chat';
import { useAuth } from '@/features/auth/hooks/useAuth';

type FilterType = 'all' | 'provider' | 'customer' | 'unread';

export const AdminMessagingInterface = () => {
  const { isMobile } = useUIHelpers();
  const { toast } = useToast();
  const { user } = useAuth();
  const {
    chats,
    isLoading,
    isConnected,
    error,
    loadChats,
    reconnect
  } = useChat();

  // Auto-select chat based on URL parameters
  const { userIdParam, isAutoSelecting } = useAutoSelectChat();

  // State management
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showChatArea, setShowChatArea] = useState(!isMobile);

  // Calculate filter counts
  const filterCounts = useMemo(() => {
    if (!chats) return { all: 0, provider: 0, customer: 0, unread: 0 };
    
    return chats.reduce((counts, chat) => {
      const otherParticipant = chat.participants.find(p => p.role.name !== 'Supreme Admin');
      const participantRole = otherParticipant?.role.name;
      
      counts.all++;
      if (chat.unread_count > 0) counts.unread++;
      if (participantRole === 'provider') counts.provider++;
      if (participantRole === 'customer') counts.customer++;
      
      return counts;
    }, { all: 0, provider: 0, customer: 0, unread: 0 });
  }, [chats]);

  // Helper function to get the most relevant timestamp for sorting
  const getChatSortTimestamp = useCallback((chat: Chat): number => {
    // Priority: last_message.created_at > updated_at > created_at
    if (chat.last_message?.created_at) {
      return new Date(chat.last_message.created_at).getTime();
    }
    if (chat.updated_at) {
      return new Date(chat.updated_at).getTime();
    }
    return new Date(chat.created_at).getTime();
  }, []);

  // Filter and sort chats based on active filter, search, and latest activity
  const filteredChats = useMemo(() => {
    if (!chats) return [];

    return chats
      .filter(chat => {
        const otherParticipant = chat.participants.find(p => p.role.name !== 'Supreme Admin');
        const participantName = otherParticipant?.name || 'Unknown';
        const participantRole = otherParticipant?.role.name;

        // Search filter
        const matchesSearch = searchQuery
          ? participantName.toLowerCase().includes(searchQuery.toLowerCase())
          : true;

        // Type filter
        const matchesFilter =
          activeFilter === 'all' ||
          (activeFilter === 'unread' && chat.unread_count > 0) ||
          (activeFilter === 'provider' && participantRole === 'provider') ||
          (activeFilter === 'customer' && participantRole === 'customer');

        return matchesSearch && matchesFilter;
      })
      .sort((a, b) => {
        // Sort by most recent activity (descending - newest first)
        return getChatSortTimestamp(b) - getChatSortTimestamp(a);
      });
  }, [chats, activeFilter, searchQuery, getChatSortTimestamp]);

  // Handle chat selection
  const handleChatSelect = useCallback((chat: Chat) => {
    setSelectedChat(chat);
    if (isMobile) {
      setShowChatArea(true);
    }
  }, [isMobile]);

  // Handle chat selection from UniversalChat (when it passes chatId)
  const handleUniversalChatSelect = useCallback((chatId: string) => {
    const chat = chats?.find(c => c.id === chatId);
    if (chat) {
      handleChatSelect(chat);
    }
  }, [chats, handleChatSelect]);

  // Handle back to list (mobile)
  const handleBackToList = useCallback(() => {
    setShowChatArea(false);
    setSelectedChat(null);
  }, []);

  // Get other participant helper
  const getOtherParticipant = useCallback((chat: Chat) => {
    return chat.participants.find(p => p.role.name !== 'Supreme Admin') || chat.participants[0];
  }, []);

  // Sync selected chat with auto-selected chat from URL parameters
  const { currentChat } = useChat();
  useEffect(() => {
    if (currentChat && currentChat.id !== selectedChat?.id) {
      setSelectedChat(currentChat);
      if (isMobile) {
        setShowChatArea(true);
      }
    }
  }, [currentChat, selectedChat?.id, isMobile]);

  // Show loading state
  if (isLoading && !chats) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-64px)]">
        <div className="text-center">
          <div className="h-8 w-8 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh-64px)]">

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden min-h-0">
        {/* Enhanced Custom Sidebar - Only show when not in mobile chat view */}
        <div className={`${isMobile && showChatArea ? 'hidden' : 'flex'} flex-col w-full md:w-96 border-r border-border bg-card shadow-sm min-h-0`}>
          {/* Sidebar Header with improved spacing */}
          <div className="p-6 border-b border-border flex-shrink-0">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-card-foreground">Conversations</h3>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  {isConnected ? (
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  ) : (
                    <div className="w-2 h-2 bg-destructive rounded-full"></div>
                  )}
                  <span className={`text-xs font-medium ${isConnected ? 'text-green-600' : 'text-destructive'}`}>
                    {isConnected ? 'Online' : 'Offline'}
                  </span>
                </div>
              </div>
            </div>

            {/* Enhanced Filter Tabs */}
            <Tabs value={activeFilter} onValueChange={(value) => setActiveFilter(value as FilterType)}>
              <TabsList className="grid w-full grid-cols-4 bg-muted p-1 rounded-lg">
                <TabsTrigger
                  value="all"
                  className="text-xs font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200"
                >
                  All
                  {filterCounts.all > 0 && (
                    <Badge variant="secondary" className="ml-1.5 text-xs">
                      {filterCounts.all}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger
                  value="provider"
                  className="text-xs font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200"
                >
                  Provider
                  {filterCounts.provider > 0 && (
                    <Badge variant="success" className="ml-1.5 text-xs">
                      {filterCounts.provider}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger
                  value="customer"
                  className="text-xs font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200"
                >
                  Customer
                  {filterCounts.customer > 0 && (
                    <Badge variant="business" className="ml-1.5 text-xs">
                      {filterCounts.customer}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger
                  value="unread"
                  className="text-xs font-medium data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm transition-all duration-200"
                >
                  Unread
                  {filterCounts.unread > 0 && (
                    <Badge variant="destructive" className="ml-1.5 text-xs">
                      {filterCounts.unread}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Enhanced Search Bar */}
          <div className="px-6 py-4 border-b border-border flex-shrink-0">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-muted focus:bg-background transition-colors duration-200"
              />
            </div>
          </div>

          {/* Enhanced Chat List - Scrollable with improved design */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <ScrollArea className="h-full">
              <div className="px-3 py-2 space-y-1">
                {filteredChats.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                      <MessageSquare className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h4 className="text-sm font-medium text-foreground mb-1">
                      {searchQuery ? 'No matches found' : 'No conversations yet'}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {searchQuery ? 'Try adjusting your search terms' : 'New conversations will appear here'}
                    </p>
                  </div>
                ) : (
                  filteredChats.map((chat) => {
                    const otherParticipant = getOtherParticipant(chat);
                    const isSelected = selectedChat?.id === chat.id;
                    const hasUnreadMessages = (chat.unread_count || 0) > 0;

                    return (
                      <div
                        key={chat.id}
                        className={`group relative p-4 cursor-pointer transition-all duration-200 rounded-xl border ${
                          isSelected
                            ? 'bg-primary/5 border-primary/20 shadow-sm'
                            : 'bg-card border-transparent hover:bg-muted/50 hover:border-border'
                        }`}
                        onClick={() => handleChatSelect(chat)}
                      >
                        {/* Unread indicator */}
                        {hasUnreadMessages && !isSelected && (
                          <div className="absolute left-1 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-primary rounded-full"></div>
                        )}

                        <div className="flex items-start space-x-3">
                          {/* Enhanced Avatar with online status */}
                          <div className="relative flex-shrink-0">
                            <Avatar className="h-12 w-12 ring-2 ring-background shadow-sm">
                              <AvatarImage src={otherParticipant?.avatar} />
                              <AvatarFallback className="bg-primary text-primary-foreground font-semibold">
                                {otherParticipant?.name?.slice(0, 2).toUpperCase() || 'UN'}
                              </AvatarFallback>
                            </Avatar>
                            {/* Online status indicator */}
                            <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-500 border-2 border-background rounded-full"></div>
                          </div>

                          <div className="flex-1 min-w-0">
                            {/* Header row with name and timestamp */}
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center space-x-2 min-w-0">
                                <h4 className={`text-sm truncate ${
                                  hasUnreadMessages ? 'font-semibold text-foreground' : 'font-medium text-foreground'
                                }`}>
                                  {otherParticipant?.name || 'Unknown User'}
                                </h4>
                                <Badge
                                  variant={otherParticipant?.role.name === 'provider' ? 'success' : 'business'}
                                  className="text-xs px-2 py-0.5 font-medium"
                                >
                                  {otherParticipant?.role.name === 'provider' ? (
                                    <UserCheck className="h-3 w-3 mr-1" />
                                  ) : (
                                    <Users className="h-3 w-3 mr-1" />
                                  )}
                                  {otherParticipant?.role.name}
                                </Badge>
                              </div>

                              <div className="flex items-center space-x-2 flex-shrink-0">
                                {hasUnreadMessages && (
                                  <Badge variant="destructive" className="text-xs px-2 py-0.5">
                                    {chat.unread_count}
                                  </Badge>
                                )}
                                <span className="text-xs text-muted-foreground">
                                  {chat.updated_at ? new Date(chat.updated_at).toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                  }) : ''}
                                </span>
                              </div>
                            </div>

                            {/* Message preview */}
                            <p className={`text-xs truncate ${
                              hasUnreadMessages ? 'text-foreground font-medium' : 'text-muted-foreground'
                            }`}>
                              {chat.last_message?.message || 'No messages yet'}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Chat Area with UniversalChat (External Sidebar Mode) */}
        <div className={`${isMobile && !showChatArea ? 'hidden' : 'flex'} flex-1 flex-col min-h-0`}>
          {selectedChat ? (
            <div className="flex-1 flex flex-col min-h-0">
              {/* Mobile Back Button */}
              {isMobile && (
                <div className="p-4 border-b bg-white flex-shrink-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToList}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    Back to conversations
                  </Button>
                </div>
              )}
              
              {/* UniversalChat with External Sidebar */}
              <div className="flex-1 min-h-0">
                <UniversalChat
                  userRole="admin"
                  userId={user?.id || 'admin-user'}
                  recipientId={getOtherParticipant(selectedChat)?.id}
                  chatId={selectedChat.id}
                  variant="full"
                  theme="admin"
                  externalSidebar={true}
                  hideHeader={true}
                  className="h-full"
                  onChatSelect={handleUniversalChatSelect}
                  onMessageSent={(message) => {
                    console.log('Message sent:', message);
                  }}
                  onError={(error) => {
                    toast({
                      title: "Chat Error",
                      description: error,
                      variant: "destructive"
                    });
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-muted/30">
              <div className="text-center max-w-md mx-auto px-6">
                <div className="w-20 h-20 bg-card rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg border border-border">
                  <Mail className="h-10 w-10 text-primary" />
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-3">
                  Welcome to Admin Messages
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  Select a conversation from the sidebar to start messaging with providers and customers.
                  All your communications are organized and easily accessible here.
                </p>
                <div className="mt-6 flex items-center justify-center space-x-4 text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Real-time messaging</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span>Secure & private</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};